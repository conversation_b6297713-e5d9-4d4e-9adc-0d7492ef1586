# 角色软删除功能说明

## 概述

角色删除功能已改为软删除模式，用户删除角色后：
- 角色数据仍保留在数据库中
- 角色不会在列表中显示
- 用户体验上等同于角色被删除
- 管理员可以在后台查看和恢复已删除的角色

## 软删除实现

### 数据库字段
在Character模型中添加了以下字段：
- `is_deleted`: 布尔字段，标记角色是否已删除
- `deleted_at`: 时间字段，记录删除时间

### 查询过滤
- **默认查询**: `Character.objects.all()` 自动过滤已删除角色
- **包含已删除**: `Character.objects.all_with_deleted()` 查询所有角色
- **只查询已删除**: `Character.objects.deleted_only()` 只查询已删除角色

## API行为变化

### DELETE /api/v1/characters/{character_id}

#### 之前（硬删除）
```json
{
  "message": "Character deleted successfully"
}
```

#### 现在（软删除）
```json
{
  "message": "Character deleted successfully",
  "deleted_at": "2024-01-15T12:30:00Z"
}
```

### GET /api/v1/characters/
- 已删除的角色不会在列表中显示
- 分页统计不包含已删除的角色

### GET /api/v1/characters/{character_id}
- 已删除的角色返回404 Not Found
- 用户无法通过API访问已删除的角色

## 管理员功能

### Django Admin增强
管理员可以在后台：
- 查看所有角色（包含已删除）
- 批量软删除角色
- 批量恢复已删除角色
- 按删除状态筛选角色

### 管理员操作
```python
# 查看已删除的角色
deleted_characters = Character.objects.deleted_only()

# 恢复角色
character = Character.objects.all_with_deleted().get(id=character_id)
if character.is_deleted:
    character.restore()
```

## 数据一致性

### 关联数据处理
- **生成任务**: 保持与角色的关联关系
- **文件存储**: 角色文件不会被自动删除
- **用户统计**: 已删除角色不计入用户角色数量

### 查询性能
- 添加了`is_deleted`字段的数据库索引
- 默认查询自动过滤，性能影响最小

## 使用示例

### 前端处理
```javascript
// 删除角色
const deleteCharacter = async (characterId, token) => {
  try {
    const response = await fetch(`/api/v1/characters/${characterId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('删除成功:', result.deleted_at);
      // 从列表中移除角色
      removeCharacterFromList(characterId);
    }
  } catch (error) {
    console.error('删除失败:', error);
  }
};
```

### 错误处理
```javascript
// 尝试访问已删除的角色
const getCharacter = async (characterId, token) => {
  try {
    const response = await fetch(`/api/v1/characters/${characterId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.status === 404) {
      console.log('角色不存在或已被删除');
    }
  } catch (error) {
    console.error('获取角色失败:', error);
  }
};
```

## 数据库迁移

### 迁移文件
需要运行以下迁移来添加软删除字段：
```bash
python manage.py migrate characters
```

### 现有数据
- 现有角色的`is_deleted`默认为`False`
- `deleted_at`默认为`NULL`
- 不影响现有功能

## 监控和维护

### 日志记录
软删除操作会记录详细日志：
```
Soft deleted character {character_id} ({character_name}) for user {user_id}
```

### 数据清理建议
```python
# 定期清理长期未使用的已删除角色（可选）
from datetime import timedelta
from django.utils import timezone

# 删除超过1年的已删除角色
old_deleted_characters = Character.objects.deleted_only().filter(
    deleted_at__lt=timezone.now() - timedelta(days=365)
)
# 可以选择硬删除这些角色
```

## 安全考虑

### 数据保护
- ✅ 防止意外数据丢失
- ✅ 保留审计追踪
- ✅ 支持数据恢复

### 隐私考虑
- ⚠️ 已删除数据仍存储在数据库中
- ⚠️ 需要考虑数据保护法规要求
- ⚠️ 建议定期清理长期未使用的数据

## 最佳实践

### 开发建议
1. **前端处理**: 删除后立即从UI中移除角色
2. **错误处理**: 正确处理404错误（角色不存在或已删除）
3. **用户提示**: 明确告知用户删除操作的影响
4. **数据备份**: 定期备份重要数据

### 运维建议
1. **监控删除频率**: 观察用户删除行为模式
2. **定期清理**: 制定数据清理策略
3. **性能监控**: 关注查询性能变化
4. **恢复流程**: 建立数据恢复标准流程

## 版本兼容性

- **向后兼容**: API响应格式略有变化（增加deleted_at字段）
- **数据库兼容**: 需要运行迁移添加新字段
- **功能增强**: 不影响现有功能，只是删除行为改变
