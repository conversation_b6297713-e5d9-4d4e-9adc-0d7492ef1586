# API 文档

本文件基于 architecture.md 的系统架构设计，描述了 Mokta 后端系统的所有 API 接口。

## 基本信息

- **Base URL**: `https://pqqjrlgoetaa.usw.sealos.io`
- **API 版本**: v1
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`

## 认证与授权 API

### 1. Google OAuth 登录
```http
POST /api/v1/login/google
```

**实现函数**: `google_login` - [`apps/authentication/views.py:14-72`](../apps/authentication/views.py#L14-L72)

**请求体**:
```json
{
  "id_token": "string"  // Google OAuth ID Token
}
```

**响应**:
```json
{
  "access_token": "string",  // JWT token
  "user": {
    "id": "uuid",
    "email": "string",
    "nickname": "string",
    "avatar_url": "string"
  }
}
```

### 2. Apple OAuth 登录
```http
POST /api/v1/login/apple
```

**实现函数**: `apple_login` - [`apps/authentication/views.py:75-127`](../apps/authentication/views.py#L75-L127)

**请求体**:
```json
{
  "id_token": "string"  // Apple OAuth ID Token
}
```

**响应**:
```json
{
  "access_token": "string",  // JWT token
  "user": {
    "id": "uuid",
    "email": "string",
    "nickname": "string",
    "avatar_url": "string"
  }
}
```

## 用户管理 API

### 3. 获取用户信息
```http
GET /api/v1/users/me
```

**实现状态**: ✅ **已实现** - `user_profile` - [`apps/users/views.py:31-54`](../apps/users/views.py#L31-L54)

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应**:
```json
{
  "id": "uuid",
  "email": "string",
  "nickname": "string",
  "avatar_url": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 4. 更新用户信息
```http
PUT /api/v1/users/me
```

**实现状态**: ✅ **已实现** - `user_profile` - [`apps/users/views.py:31-54`](../apps/users/views.py#L31-L54)

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "nickname": "string",
  "avatar_url": "string"
}
```

**响应**:
```json
{
  "id": "uuid",
  "email": "string",
  "nickname": "string",
  "avatar_url": "string",
  "updated_at": "datetime"
}
```

### 5. 删除用户账号
```http
DELETE /api/v1/users/me/delete
```

**实现状态**: ✅ **已实现** - `delete_user_account` - [`apps/users/views.py:57-77`](../apps/users/views.py#L57-L77)

**权限**: 需要认证

**功能**: 软删除用户账号，用户数据保留在数据库中但标记为已删除，无法再次登录

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "confirm_delete": true
}
```

**响应**:
```json
{
  "message": "Account deleted successfully",
  "deleted_at": "datetime"
}
```

**错误响应**:
```json
{
  "confirm_delete": ["必须确认删除操作"]
}
```

**注意事项**:
- 删除后用户无法通过OAuth重新登录
- 用户数据仍保留在数据库中（软删除）
- 删除操作不可逆（除非管理员手动恢复）
- 用户的所有关联数据（角色、任务等）仍然存在

## 文件上传 API

### 5. 获取图片上传 URL
```http
POST /api/v1/upload/presigned-url
```

**实现函数**: `get_presigned_upload_url` - [`apps/uploads/views.py:13-75`](../apps/uploads/views.py#L13-L75)

**权限**: 需要认证

**功能**: 生成文件直传MinIO/S3的预签名URL，支持JPEG/PNG格式，最大10MB

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "file_name": "string",
  "file_type": "string",  // image/jpeg, image/png
  "file_size": "integer"   // 最大10MB
}
```

**响应**:
```json
{
  "upload_url": "string",    // 对象存储预签名上传URL
  "file_key": "string",      // 对象存储object key
  "expires_at": "datetime"   // URL过期时间
}
```

## 角色生成 API

### 6. 创建角色生成任务
```http
POST /api/v1/characters/generate
```

**实现函数**: `create_character_generation_task` - [`apps/characters/views.py:16-105`](../apps/characters/views.py#L16-L105)

**权限**: 需要认证

**功能**: 创建3D模型生成任务，检查每日限额，验证文件存在性，通过Celery发送到GPU服务器

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "source_photo_key": "string",  // 对象存储文件 key
  "quality": "string"           // 生成质量: "high", "medium", "low", "flux", "test" (默认: "high")
}
```

**响应**:
```json
{
  "task_id": "uuid",
  "status": "PENDING",
  "message": "3D character generation task created",
  "quality": "high"
}
```

### 7. 查询任务状态
```http
GET /api/v1/tasks/{task_id}/status
```

**实现函数**: `get_task_status` - [`apps/tasks/views.py:27-101`](../apps/tasks/views.py#L27-L101)

**权限**: 需要认证，只能查询自己的任务

**功能**: 查询Celery任务状态，同步数据库记录，返回进度和结果信息

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**任务等待响应**:
```json
{
  "task_id": "uuid",
  "status": "PENDING",
  "progress": 0,
  "message": "Task is waiting to be processed"
}
```

**任务进行中响应**:
```json
{
  "task_id": "uuid",
  "status": "PROCESSING",
  "progress": 45,  // 进度百分比
  "message": "正在生成3D模型...",
  "current_step": "processing_3d"  // 当前步骤: processing_img|processing_3d|processing_remesh|processing_rigging|uploading
}
```

**任务重试响应**:
```json
{
  "task_id": "uuid",
  "status": "RETRY",
  "progress": 0,
  "message": "重试中... (2/3)",
  "retry_count": 2
}
```

**任务成功响应**:
```json
{
  "task_id": "uuid",
  "status": "SUCCESS",
  "progress": 100,
  "message": "处理完成",
  "result": {
    "status": "success",
    "model_url": "string",
    "thumbnail_url": "string"  // 对象存储缩略图文件 URL
  }
}
```

**任务失败响应**:
```json
{
  "task_id": "uuid",
  "status": "FAILURE",
  "error_message": "处理失败: connection timeout",
  "error": "connection timeout",
  "progress": 0,
  "retry_count": 2
}
```

## 角色库管理 API

### 8. 获取用户角色列表
```http
GET /api/v1/characters/
# 注意最后有个斜杠
```

**实现函数**: `get_user_characters` - [`apps/characters/views.py:111-173`](../apps/characters/views.py#L111-L173)

**权限**: 需要认证

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**查询参数**:
- `page`: 页码（默认: 1）
- `page_size`: 每页数量（默认: 12，最大: 50）

**响应**:
```json
{
  "total": 42,
  "page": 1,
  "page_size": 12,
  "total_pages": 3,
  "characters": [
    {
      "id": "uuid",
      "name": "string",
      "model_url": "string",
      "thumbnail_url": "string",
      "metadata": {},
      "created_at": "datetime"
    }
  ]
}
```

### 9. 获取角色详情
```http
GET /api/v1/characters/{character_id}
```

**实现状态**: ❌ **未实现** - 需要在 `apps/characters/views.py` 中添加实现

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应**:
```json
{
  "id": "uuid",
  "name": "string",
  "model_url": "string",
  "thumbnail_url": "string",
  "metadata": {
    "animations": ["idle", "walk", "run"],
    "effects": ["glow", "particles"]
  },
  "created_at": "datetime"
}
```

### 10. 更新角色
```http
PUT /api/v1/characters/{character_id}
```

**实现状态**: ❌ **未实现** - 需要在 `apps/characters/views.py` 中添加实现

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**请求体**:
```json
{
  "name": "string",
  "metadata": {}
}
```

**响应**:
```json
{
  "id": "uuid",
  "name": "string",
  "model_url": "string",
  "thumbnail_url": "string",
  "metadata": {},
  "updated_at": "datetime"
}
```

### 11. 删除角色
```http
DELETE /api/v1/characters/{character_id}
```

**实现状态**: ❌ **未实现** - 需要在 `apps/characters/views.py` 中添加实现

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应**:
```json
{
  "message": "Character deleted successfully"
}
```

## 使用额度 API

### 12. 获取今日使用情况
```http
GET /api/v1/users/me/usage/today
```

**实现函数**: `get_today_usage` - [`apps/users/views.py:8-27`](../apps/users/views.py#L8-L27)

**权限**: 需要认证

**功能**: 统计用户今日生成次数，检查是否达到每日限额，管理员无限制

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应**:
```json
{
  "date": "2024-01-15",
  "total_generations": 3,
  "daily_limit": 5,
  "remaining": 2,
  "can_generate": true,
  "is_admin": false
}
```

## 动作库管理 API

### 13. 获取所有动作列表
```http
GET /api/v1/motions/
```

**实现函数**: `get_all_motions` - [`apps/motions/views.py:13-73`](../apps/motions/views.py#L13-L73)

**权限**: 需要认证

**功能**: 获取系统中所有用户创建的动作列表（公共动作库）

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**查询参数**:
- `page`: 页码（默认: 1）
- `page_size`: 每页数量（默认: 12，最大: 50）

**响应**:
```json
{
  "total": 25,
  "page": 1,
  "page_size": 12,
  "total_pages": 3,
  "motions": [
    {
      "id": "uuid",
      "name": "跑步动作",
      "motion_url": "string",
      "preview_video_url": "string",
      "creator": "uuid",
      "metadata": {},
      "created_at": "datetime"
    }
  ]
}
```

## 管理界面

### Django Admin 后台管理
**访问地址**: `https://pqqjrlgoetaa.usw.sealos.io/admin/`

**权限要求**: 只有管理员用户（`provider='admin'`）可以访问

**功能特性**:
- **动作库管理**: 查看、编辑、删除所有动作记录
- **文件上传**: 运营人员可直接上传动作文件到对象存储
- **用户管理**: 管理用户账户和权限
- **角色管理**: 查看和管理生成的角色
- **任务监控**: 查看任务执行状态和历史

**动作管理功能**:
- 列表显示：动作名称、创建者、文件URL、预览视频、创建时间
- 搜索功能：按动作名称、创建者邮箱搜索
- 筛选功能：按创建时间、创建者筛选
- 批量操作：批量删除、导出等
- 文件上传：支持FBX、BVH等动作文件格式（最大50MB）
- 视频上传：支持MP4、MOV等预览视频格式（最大100MB）

**管理员账户创建**:
```bash
python manage.py createsuperuser
```

## 通用错误响应

所有 API 接口可能返回以下错误响应：

### 401 未认证
```json
{
  "error": "unauthorized",
  "message": "Invalid access token"
}
```

### 403 无权限
```json
{
  "error": "forbidden",
  "message": "You don't have permission to access this resource"
}
```

### 404 未找到
```json
{
  "error": "not_found",
  "message": "The requested resource does not exist"
}
```

### 429 超出速率限制
```json
{
  "error": "rate_limit_exceeded",
  "message": "Too many requests, please try again later"
}
```

### 429 超出每日限额
```json
{
  "error": "daily_limit_exceeded",
  "message": "今日生成次数已达上限（3/5）",
  "today_usage": {
    "total_generations": 3,
    "daily_limit": 5,
    "remaining": 2
  }
}
```

### 500 服务器内部错误
```json
{
  "error": "internal_server_error",
  "message": "Internal server error"
}
```

## 状态码

- `200`: 请求成功
- `201`: 资源创建成功
- `204`: 请求成功，无返回内容
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 无权限
- `404`: 资源未找到
- `429`: 超出速率限制
- `500`: 服务器内部错误

## 认证

除登录接口外，所有 API 都需要在请求头中携带有效的 JWT token：

```
Authorization: Bearer <your_jwt_token>
```

JWT token 有效期为 7 天，过期后需重新登录获取新 token。

## 速率限制

- 认证相关 API：每分钟 10 次
- 文件上传 API：每分钟 20 次
- 角色生成 API：每分钟 5 次
- 其他 API：每分钟 100 次

## 数据格式

- 所有 datetime 字段均为 ISO 8601 格式: `2024-01-15T10:30:00Z`
- UUID 字段为标准格式: `550e8400-e29b-41d4-a716-************`
- 文件大小单位为字节
- 进度值为 0-100 的整数