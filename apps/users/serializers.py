from rest_framework import serializers
from .models import User


class UserSerializer(serializers.ModelSerializer):
    """用户信息序列化器 - 用于GET请求"""

    class Meta:
        model = User
        fields = [
            'id', 'email', 'nickname', 'avatar_url',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'email', 'created_at', 'updated_at']


class UserUpdateResponseSerializer(serializers.ModelSerializer):
    """用户信息更新响应序列化器 - 用于PUT请求响应"""

    class Meta:
        model = User
        fields = [
            'id', 'email', 'nickname', 'avatar_url',
            'updated_at'
        ]
        read_only_fields = ['id', 'email', 'updated_at']


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器 - 用于PUT请求输入"""

    class Meta:
        model = User
        fields = ['nickname', 'avatar_url']

    def validate_nickname(self, value):
        """验证昵称"""
        if value and len(value.strip()) == 0:
            raise serializers.ValidationError("昵称不能为空")
        return value.strip() if value else value

    def validate_avatar_url(self, value):
        """验证头像URL"""
        if value and not value.startswith(('http://', 'https://')):
            raise serializers.ValidationError("头像URL格式不正确")
        return value
