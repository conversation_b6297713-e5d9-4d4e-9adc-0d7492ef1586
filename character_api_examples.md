# 角色管理API使用说明

## 已实现的API

### 1. 获取角色详情
**端点**: `GET /api/v1/characters/{character_id}`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "我的3D角色",
  "model_url": "https://storage.example.com/models/character.glb",
  "thumbnail_url": "https://storage.example.com/thumbnails/character.jpg",
  "metadata": {
    "animations": ["idle", "walk", "run"],
    "effects": ["glow", "particles"]
  },
  "created_at": "2024-01-15T10:30:00Z"
}
```

### 2. 更新角色信息
**端点**: `PUT /api/v1/characters/{character_id}`

**请求头**:
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "更新后的角色名称",
  "metadata": {
    "animations": ["idle", "walk", "run", "jump"],
    "effects": ["glow", "particles", "sparkle"]
  }
}
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "更新后的角色名称",
  "model_url": "https://storage.example.com/models/character.glb",
  "thumbnail_url": "https://storage.example.com/thumbnails/character.jpg",
  "metadata": {
    "animations": ["idle", "walk", "run", "jump"],
    "effects": ["glow", "particles", "sparkle"]
  },
  "updated_at": "2024-01-15T14:20:00Z"
}
```

### 3. 删除角色
**端点**: `DELETE /api/v1/characters/{character_id}`

**请求头**:
```
Authorization: Bearer <jwt_token>
```

**响应示例**:
```json
{
  "message": "Character deleted successfully"
}
```

## 实现特性

### 权限控制
- ✅ 用户只能访问、修改和删除自己的角色
- ✅ JWT认证保护所有端点
- ✅ 404错误用于不存在或无权限访问的角色

### 数据验证
- ✅ 角色名称不能为空且不超过100个字符
- ✅ 支持部分更新（partial update）
- ✅ metadata字段支持任意JSON数据

### 错误处理
- ✅ 统一的错误响应格式
- ✅ 详细的日志记录
- ✅ 正确的HTTP状态码

## 使用curl测试

### 获取角色详情
```bash
curl -X GET "https://pqqjrlgoetaa.usw.sealos.io/api/v1/characters/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 更新角色信息
```bash
curl -X PUT "https://pqqjrlgoetaa.usw.sealos.io/api/v1/characters/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新的角色名称",
    "metadata": {
      "animations": ["idle", "walk"],
      "effects": ["glow"]
    }
  }'
```

### 删除角色
```bash
curl -X DELETE "https://pqqjrlgoetaa.usw.sealos.io/api/v1/characters/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 前端集成示例

### JavaScript/React示例
```javascript
// 获取角色详情
const getCharacterDetail = async (characterId, token) => {
  try {
    const response = await fetch(`/api/v1/characters/${characterId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const character = await response.json();
      return character;
    } else {
      throw new Error('Failed to fetch character');
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};

// 更新角色信息
const updateCharacter = async (characterId, updateData, token) => {
  try {
    const response = await fetch(`/api/v1/characters/${characterId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    if (response.ok) {
      const updatedCharacter = await response.json();
      return updatedCharacter;
    } else {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update character');
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};

// 删除角色
const deleteCharacter = async (characterId, token) => {
  try {
    const response = await fetch(`/api/v1/characters/${characterId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      throw new Error('Failed to delete character');
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};
```

## 错误响应格式

### 404 角色不存在
```json
{
  "error": "Character not found"
}
```

### 400 验证错误
```json
{
  "name": ["角色名称不能为空"]
}
```

### 401 未认证
```json
{
  "error": "unauthorized",
  "message": "Invalid access token"
}
```

### 500 服务器错误
```json
{
  "error": "Failed to process character request"
}
```

## 注意事项

1. **UUID格式**: character_id必须是有效的UUID格式
2. **权限隔离**: 用户只能操作自己的角色
3. **数据完整性**: 删除角色是永久性的，无法恢复
4. **文件清理**: 删除角色时不会自动删除对象存储中的文件
5. **并发安全**: 支持并发操作，使用数据库事务保证一致性
